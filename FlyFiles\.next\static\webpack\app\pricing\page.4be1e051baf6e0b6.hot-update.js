"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pricing/page",{

/***/ "(app-pages-browser)/./src/app/lib/plans.ts":
/*!******************************!*\
  !*** ./src/app/lib/plans.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGS: () => (/* binding */ PLAN_CONFIGS)\n/* harmony export */ });\n// Plan configurations - separated from MongoDB to avoid client-side bundling issues\nconst PLAN_CONFIGS = {\n    guest: {\n        name: 'Gæst',\n        uploadLimit: {\n            amount: 250 * 1024 * 1024,\n            period: 'session'\n        },\n        fileExpiry: 7,\n        downloadLimits: {\n            configurable: false,\n            unlimited: true\n        },\n        features: [\n            '250MB pr. session',\n            '7 dages filudløb',\n            'Ubegrænsede downloads',\n            'Ingen konto nødvendig'\n        ]\n    },\n    free: {\n        name: 'Gratis Konto',\n        uploadLimit: {\n            amount: 15 * 1024 * 1024 * 1024,\n            period: 'måned'\n        },\n        fileExpiry: 10,\n        downloadLimits: {\n            configurable: true,\n            unlimited: false\n        },\n        features: [\n            '15GB månedlig upload',\n            '10-dages filudløb',\n            'Konfigurerbare downloadgrænser',\n            'Google login påkrævet'\n        ]\n    },\n    upgrade1: {\n        name: 'Opgradering 1',\n        uploadLimit: {\n            amount: 15 * 1024 * 1024 * 1024,\n            period: 'uge'\n        },\n        fileExpiry: 14,\n        downloadLimits: {\n            configurable: true,\n            unlimited: false\n        },\n        features: [\n            '15GB ugentlig upload',\n            '14-dages filudløb',\n            'Download statistik',\n            'Alle Gratis funktioner'\n        ],\n        price: {\n            amount: 5,\n            period: 'måned'\n        }\n    },\n    upgrade2: {\n        name: 'Opgradering 2',\n        uploadLimit: {\n            amount: 50 * 1024 * 1024 * 1024,\n            period: 'uge'\n        },\n        fileExpiry: 30,\n        downloadLimits: {\n            configurable: true,\n            unlimited: true\n        },\n        features: [\n            '50GB ugentlig upload',\n            '30-dages filudløb',\n            'Ubegrænsede downloads',\n            'Avanceret analyse',\n            'Alle tidligere funktioner'\n        ],\n        price: {\n            amount: 25,\n            period: 'måned'\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL3BsYW5zLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxvRkFBb0Y7QUFDN0UsTUFBTUEsZUFBZTtJQUMxQkMsT0FBTztRQUNMQyxNQUFNO1FBQ05DLGFBQWE7WUFDWEMsUUFBUSxNQUFNLE9BQU87WUFDckJDLFFBQVE7UUFDVjtRQUNBQyxZQUFZO1FBQ1pDLGdCQUFnQjtZQUNkQyxjQUFjO1lBQ2RDLFdBQVc7UUFDYjtRQUNBQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7U0FDRDtJQUNIO0lBQ0FDLE1BQU07UUFDSlQsTUFBTTtRQUNOQyxhQUFhO1lBQ1hDLFFBQVEsS0FBSyxPQUFPLE9BQU87WUFDM0JDLFFBQVE7UUFDVjtRQUNBQyxZQUFZO1FBQ1pDLGdCQUFnQjtZQUNkQyxjQUFjO1lBQ2RDLFdBQVc7UUFDYjtRQUNBQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7U0FDRDtJQUNIO0lBQ0FFLFVBQVU7UUFDUlYsTUFBTTtRQUNOQyxhQUFhO1lBQ1hDLFFBQVEsS0FBSyxPQUFPLE9BQU87WUFDM0JDLFFBQVE7UUFDVjtRQUNBQyxZQUFZO1FBQ1pDLGdCQUFnQjtZQUNkQyxjQUFjO1lBQ2RDLFdBQVc7UUFDYjtRQUNBQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNERyxPQUFPO1lBQ0xULFFBQVE7WUFDUkMsUUFBUTtRQUNWO0lBQ0Y7SUFDQVMsVUFBVTtRQUNSWixNQUFNO1FBQ05DLGFBQWE7WUFDWEMsUUFBUSxLQUFLLE9BQU8sT0FBTztZQUMzQkMsUUFBUTtRQUNWO1FBQ0FDLFlBQVk7UUFDWkMsZ0JBQWdCO1lBQ2RDLGNBQWM7WUFDZEMsV0FBVztRQUNiO1FBQ0FDLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREcsT0FBTztZQUNMVCxRQUFRO1lBQ1JDLFFBQVE7UUFDVjtJQUNGO0FBQ0YsRUFBVyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNaWNrYVxcQ2FzY2FkZVByb2plY3RzXFxGbHlGaWxlc1xcc3JjXFxhcHBcXGxpYlxccGxhbnMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUGxhbiBjb25maWd1cmF0aW9ucyAtIHNlcGFyYXRlZCBmcm9tIE1vbmdvREIgdG8gYXZvaWQgY2xpZW50LXNpZGUgYnVuZGxpbmcgaXNzdWVzXG5leHBvcnQgY29uc3QgUExBTl9DT05GSUdTID0ge1xuICBndWVzdDoge1xuICAgIG5hbWU6ICdHw6ZzdCcsXG4gICAgdXBsb2FkTGltaXQ6IHtcbiAgICAgIGFtb3VudDogMjUwICogMTAyNCAqIDEwMjQsIC8vIDI1ME1CXG4gICAgICBwZXJpb2Q6ICdzZXNzaW9uJyBhcyBjb25zdFxuICAgIH0sXG4gICAgZmlsZUV4cGlyeTogNywgLy8gZGFnZVxuICAgIGRvd25sb2FkTGltaXRzOiB7XG4gICAgICBjb25maWd1cmFibGU6IGZhbHNlLFxuICAgICAgdW5saW1pdGVkOiB0cnVlXG4gICAgfSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJzI1ME1CIHByLiBzZXNzaW9uJyxcbiAgICAgICc3IGRhZ2VzIGZpbHVkbMO4YicsXG4gICAgICAnVWJlZ3LDpm5zZWRlIGRvd25sb2FkcycsXG4gICAgICAnSW5nZW4ga29udG8gbsO4ZHZlbmRpZydcbiAgICBdXG4gIH0sXG4gIGZyZWU6IHtcbiAgICBuYW1lOiAnR3JhdGlzIEtvbnRvJyxcbiAgICB1cGxvYWRMaW1pdDoge1xuICAgICAgYW1vdW50OiAxNSAqIDEwMjQgKiAxMDI0ICogMTAyNCwgLy8gMTVHQlxuICAgICAgcGVyaW9kOiAnbcOlbmVkJyBhcyBjb25zdFxuICAgIH0sXG4gICAgZmlsZUV4cGlyeTogMTAsIC8vIGRhZ2VcbiAgICBkb3dubG9hZExpbWl0czoge1xuICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgdW5saW1pdGVkOiBmYWxzZVxuICAgIH0sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICcxNUdCIG3DpW5lZGxpZyB1cGxvYWQnLFxuICAgICAgJzEwLWRhZ2VzIGZpbHVkbMO4YicsXG4gICAgICAnS29uZmlndXJlcmJhcmUgZG93bmxvYWRncsOmbnNlcicsXG4gICAgICAnR29vZ2xlIGxvZ2luIHDDpWtyw6Z2ZXQnXG4gICAgXVxuICB9LFxuICB1cGdyYWRlMToge1xuICAgIG5hbWU6ICdPcGdyYWRlcmluZyAxJyxcbiAgICB1cGxvYWRMaW1pdDoge1xuICAgICAgYW1vdW50OiAxNSAqIDEwMjQgKiAxMDI0ICogMTAyNCwgLy8gMTVHQlxuICAgICAgcGVyaW9kOiAndWdlJyBhcyBjb25zdFxuICAgIH0sXG4gICAgZmlsZUV4cGlyeTogMTQsIC8vIGRhZ2VcbiAgICBkb3dubG9hZExpbWl0czoge1xuICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgdW5saW1pdGVkOiBmYWxzZVxuICAgIH0sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICcxNUdCIHVnZW50bGlnIHVwbG9hZCcsXG4gICAgICAnMTQtZGFnZXMgZmlsdWRsw7hiJyxcbiAgICAgICdEb3dubG9hZCBzdGF0aXN0aWsnLFxuICAgICAgJ0FsbGUgR3JhdGlzIGZ1bmt0aW9uZXInXG4gICAgXSxcbiAgICBwcmljZToge1xuICAgICAgYW1vdW50OiA1LFxuICAgICAgcGVyaW9kOiAnbcOlbmVkJyBhcyBjb25zdFxuICAgIH1cbiAgfSxcbiAgdXBncmFkZTI6IHtcbiAgICBuYW1lOiAnT3BncmFkZXJpbmcgMicsXG4gICAgdXBsb2FkTGltaXQ6IHtcbiAgICAgIGFtb3VudDogNTAgKiAxMDI0ICogMTAyNCAqIDEwMjQsIC8vIDUwR0JcbiAgICAgIHBlcmlvZDogJ3VnZScgYXMgY29uc3RcbiAgICB9LFxuICAgIGZpbGVFeHBpcnk6IDMwLCAvLyBkYWdlXG4gICAgZG93bmxvYWRMaW1pdHM6IHtcbiAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgIHVubGltaXRlZDogdHJ1ZVxuICAgIH0sXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICc1MEdCIHVnZW50bGlnIHVwbG9hZCcsXG4gICAgICAnMzAtZGFnZXMgZmlsdWRsw7hiJyxcbiAgICAgICdVYmVncsOmbnNlZGUgZG93bmxvYWRzJyxcbiAgICAgICdBdmFuY2VyZXQgYW5hbHlzZScsXG4gICAgICAnQWxsZSB0aWRsaWdlcmUgZnVua3Rpb25lcidcbiAgICBdLFxuICAgIHByaWNlOiB7XG4gICAgICBhbW91bnQ6IDI1LFxuICAgICAgcGVyaW9kOiAnbcOlbmVkJyBhcyBjb25zdFxuICAgIH1cbiAgfVxufSBhcyBjb25zdDtcbiJdLCJuYW1lcyI6WyJQTEFOX0NPTkZJR1MiLCJndWVzdCIsIm5hbWUiLCJ1cGxvYWRMaW1pdCIsImFtb3VudCIsInBlcmlvZCIsImZpbGVFeHBpcnkiLCJkb3dubG9hZExpbWl0cyIsImNvbmZpZ3VyYWJsZSIsInVubGltaXRlZCIsImZlYXR1cmVzIiwiZnJlZSIsInVwZ3JhZGUxIiwicHJpY2UiLCJ1cGdyYWRlMiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/plans.ts\n"));

/***/ })

});