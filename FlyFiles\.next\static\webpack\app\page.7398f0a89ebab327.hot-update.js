"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ui/card */ \"(app-pages-browser)/./src/app/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,File,Shield,Upload,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,File,Shield,Upload,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,File,Shield,Upload,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,File,Shield,Upload,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,File,Shield,Upload,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,File,Shield,Upload,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedFiles, setSelectedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const MAX_FILE_SIZE = 250 * 1024 * 1024 // 250MB in bytes\n    ;\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"Home.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(true);\n        }\n    }[\"Home.useCallback[handleDragOver]\"], []);\n    const handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"Home.useCallback[handleDragLeave]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n        }\n    }[\"Home.useCallback[handleDragLeave]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"Home.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            setIsDragOver(false);\n            const files = Array.from(e.dataTransfer.files);\n            handleFileSelection(files);\n        }\n    }[\"Home.useCallback[handleDrop]\"], []);\n    const handleFileSelection = (files)=>{\n        const validFiles = files.filter((file)=>file.size <= MAX_FILE_SIZE);\n        const oversizedFiles = files.filter((file)=>file.size > MAX_FILE_SIZE);\n        if (oversizedFiles.length > 0) {\n            alert(\"\".concat(oversizedFiles.length, \" fil(er) er st\\xf8rre end 250MB og kan ikke uploades som g\\xe6st.\"));\n        }\n        setSelectedFiles((prev)=>[\n                ...prev,\n                ...validFiles\n            ]);\n    };\n    const handleFileInputChange = (e)=>{\n        if (e.target.files) {\n            const files = Array.from(e.target.files);\n            handleFileSelection(files);\n        }\n    };\n    const removeFile = (index)=>{\n        setSelectedFiles((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const handleUpload = async ()=>{\n        if (selectedFiles.length === 0) return;\n        setIsUploading(true);\n        setUploadProgress(0);\n        try {\n            const uploadedFiles = [];\n            const totalFiles = selectedFiles.length;\n            for(let i = 0; i < selectedFiles.length; i++){\n                var _result_data, _result_data1, _result_data2;\n                const file = selectedFiles[i];\n                const formData = new FormData();\n                formData.append('file', file);\n                // Update progress for current file\n                setUploadProgress(Math.round(i / totalFiles * 100));\n                const response = await fetch('/api/gofile/upload', {\n                    method: 'POST',\n                    body: formData\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || 'Upload failed');\n                }\n                const result = await response.json();\n                uploadedFiles.push({\n                    name: file.name,\n                    downloadPage: (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.downloadPage,\n                    directLink: (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.directLink,\n                    fileId: (_result_data2 = result.data) === null || _result_data2 === void 0 ? void 0 : _result_data2.fileId\n                });\n            }\n            // Complete upload\n            setUploadProgress(100);\n            setIsUploading(false);\n            setSelectedFiles([]);\n            // Show success message with download links\n            const linksList = uploadedFiles.map((file)=>\"\".concat(file.name, \": \").concat(file.downloadPage || file.directLink)).join('\\n');\n            alert(\"Filer uploadet succesfuldt!\\n\\nDownload links:\\n\".concat(linksList));\n        } catch (error) {\n            console.error('Upload error:', error);\n            setIsUploading(false);\n            alert(\"Upload fejlede: \".concat(error.message));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-b from-gray-50 to-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 md:py-28 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 sm:px-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:w-1/2 lg:pr-10 text-center lg:text-left mb-10 lg:mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default\",\n                                            children: \"Danmarks nye fildelingsplatform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 sm:mb-6 leading-tight\",\n                                            children: [\n                                                \"Send store filer \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-300\",\n                                                    children: \"sikkert\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 34\n                                                }, this),\n                                                \" og nemt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-xl mx-auto lg:mx-0\",\n                                            children: \"FlyFiles er Danmarks nye fildelingsplatform. Send filer op til 50GB med nem deling og sikker opbevaring.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n                                            children: session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"relative z-10 flex items-center justify-center\",\n                                                        children: [\n                                                            \"G\\xe5 til Dashboard\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            var _fileInputRef_current;\n                                                            return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                        },\n                                                        className: \"group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"relative z-10 flex items-center justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-5 w-5 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 164,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Upload som g\\xe6st (250MB)\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 167,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"group relative bg-transparent border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:bg-white hover:text-blue-800 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"relative z-10 flex items-center justify-center\",\n                                                            children: [\n                                                                \"Log ind for 15GB\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:w-1/2 flex justify-center lg:justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full max-w-md lg:max-w-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-fade-in\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-white\",\n                                                                            children: \"Hurtig upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 196,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-blue-200 text-sm\",\n                                                                            children: \"Op til 50GB per fil\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 197,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-white\",\n                                                                            children: \"Sikker deling\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-blue-200 text-sm\",\n                                                                            children: \"Krypteret og beskyttet\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-white\",\n                                                                            children: \"Automatisk sletning\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-blue-200 text-sm\",\n                                                                            children: \"Ingen permanente filer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            !session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"border-2 border-dashed transition-all duration-300 \".concat(isDragOver ? 'border-blue-500 bg-blue-100/50 scale-105' : 'border-blue-300 hover:border-blue-500 bg-blue-50/50'),\n                        onDragOver: handleDragOver,\n                        onDragLeave: handleDragLeave,\n                        onDrop: handleDrop,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 \".concat(isDragOver ? 'scale-110 bg-blue-700' : ''),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-800 mb-2\",\n                                            children: isDragOver ? 'Slip filerne her!' : 'Drag filer hertil eller klik for at uploade'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6\",\n                                            children: \"Som g\\xe6st kan du uploade op til 250MB per fil. Alle filtyper er tilladt. Filer udl\\xf8ber efter 7 dage.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            multiple: true,\n                                            className: \"hidden\",\n                                            onChange: handleFileInputChange,\n                                            accept: \"*/*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                var _fileInputRef_current;\n                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                            },\n                                            disabled: isUploading,\n                                            className: \"group relative bg-blue-600 text-white px-6 py-3 rounded-lg font-bold overflow-hidden transition-all duration-300 hover:bg-blue-700 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10 flex items-center justify-center\",\n                                                children: [\n                                                    isUploading ? 'Uploader...' : 'Vælg filer',\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 ml-2 transform transition-all duration-300 group-hover:translate-x-1\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 17\n                                }, this),\n                                selectedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 border-t pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                            children: [\n                                                \"Valgte filer (\",\n                                                selectedFiles.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 max-h-60 overflow-y-auto\",\n                                            children: selectedFiles.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between bg-gray-50 p-3 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-gray-800 truncate max-w-xs\",\n                                                                            children: file.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: formatFileSize(file.size)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFile(index),\n                                                            className: \"text-red-500 hover:text-red-700 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, this),\n                                        isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Uploader filer...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                uploadProgress,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(uploadProgress, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 23\n                                        }, this),\n                                        !isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleUpload,\n                                            className: \"mt-4 w-full bg-green-600 text-white py-3 rounded-lg font-bold hover:bg-green-700 transition-colors\",\n                                            children: [\n                                                \"Upload \",\n                                                selectedFiles.length,\n                                                \" fil\",\n                                                selectedFiles.length !== 1 ? 'er' : ''\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-b from-white to-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl sm:text-4xl font-bold text-gray-800 mb-4\",\n                                    children: [\n                                        \"Hvorfor v\\xe6lge \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600\",\n                                            children: \"FlyFiles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 29\n                                        }, this),\n                                        \"?\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Bygget til danskere med fokus p\\xe5 sikkerhed og brugervenlighed\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"h-full border-blue-100 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 bg-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:scale-110\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-8 w-8 text-white transition-transform duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:rotate-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-xl text-gray-800\",\n                                                    children: \"Sikker opbevaring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Dine filer krypteres og opbevares sikkert med automatisk sletning\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"h-full border-blue-100 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 bg-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:scale-110\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-8 w-8 text-white transition-transform duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:rotate-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-xl text-gray-800\",\n                                                    children: \"Automatisk udl\\xf8b\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Filer slettes automatisk efter udl\\xf8bsdatoen for din sikkerhed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"h-full border-blue-100 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 bg-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:scale-110\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_File_Shield_Upload_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-8 w-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-xl text-gray-800\",\n                                                    children: \"Dansk support\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Lokalproduceret platform med dansk kundeservice og GDPR-compliance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-r from-blue-700 to-blue-900 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl font-bold mb-4\",\n                            children: \"V\\xe6lg den plan der passer til dig\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\",\n                            children: \"Fra gratis g\\xe6steadgang til professionelle l\\xf8sninger\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/pricing\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"group relative bg-white text-blue-800 px-8 py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10 flex items-center justify-center\",\n                                        children: [\n                                            \"Se alle priser\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"lns6lmaRbOBE1475wNIsczXCODw=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});